# Flag-40数据集问题构造项目文档

## 项目概述

### 项目目标

基于**flag-40.csv**数据集构造三道高质量的分析题目，包含不带模板的归因问题、带模板的归因问题和带模板的开放问题，旨在测试大模型在企业数据分析场景下的推理能力和领域知识应用能力。

### 核心要求

- 构造三种不同类型的题目，难度递进
- 部分题目需要领域知识才能正确解答
- 提供数据集扩展建议以支持更复杂的分析场景

## 数据集分析

### 业务场景定义

**flag-40.csv**数据集来源于某大型科技企业的**ServiceNow费用管理系统**（fm_expense_line表），记录了2024年全年各部门员工提交的费用申请及其处理情况。该系统是基于ServiceNow平台构建的企业级IT服务管理解决方案，专门用于财务费用的全生命周期管理。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义                         | 取值范围                                                            |
| -------- | -------- | -------------------------------- | ------------------------------------------------------------------- |
| 编号     | UUID     | 申请单唯一标识                   | 系统生成                                                            |
| 创建时间 | DateTime | 申请提交时间                     | 2024年全年                                                          |
| 金额     | Decimal  | 申请金额（元）                   | 1,000-150,000                                                       |
| 状态     | Enum     | 申请处理状态                     | Processed/Declined/Pending/Submitted                                |
| 简短描述 | Text     | 申请内容描述                     | 包含关键词：Equipment/Cloud/Asset/Service/Travel                    |
| 配置项   | Enum     | 申请配置类型                     | heart/truth/certain/among等                                         |
| 用户     | String   | 申请人用户名                     | 员工账号                                                            |
| 部门     | Enum     | 申请人所属部门                   | HR/Finance/Development/IT/Customer Support/Sales/Product Management |
| 类别     | Enum     | 费用类别                         | Assets/Miscellaneous/Services/Travel                                |
| 处理日期 | DateTime | 申请处理完成时间                 | 2024年全年                                                          |
| 来源ID   | UUID     | 关联的业务来源                   | 系统生成                                                            |
| 类型     | Enum     | 申请类型                         | One-time/Recurring                                                  |
| 地点     | Enum     | 业务发生地区                     | North America/Africa/Asia/Europe/South America                      |
| 处理周期 | Integer  | 处理天数（可为负值表示提前处理） | -300到+300天                                                        |

## 领域知识体系

### ServiceNow平台专业术语与概念

#### ITSM核心概念
- **Configuration Item (CI)**：配置项，IT基础设施中的可管理组件
- **Service Level Agreement (SLA)**：服务级别协议，定义服务质量标准
- **Business Rule**：业务规则，自动化工作流的逻辑判断条件
- **Workflow Engine**：工作流引擎，处理审批流程的核心组件
- **Assignment Group**：分配组，负责处理特定类型请求的团队

#### 费用管理专业指标计算公式

##### 处理效率类指标
- **SLA达成率** = 在SLA时间内完成的申请数 / 总申请数 × 100%
- **平均解决时间 (MTTR)** = 总处理时间 / 已处理申请数
- **首次通过率 (FPY)** = 首次提交即通过的申请数 / 总申请数 × 100%
- **重新提交率** = 需要重新提交的申请数 / 总申请数 × 100%
- **审批层级效率** = 单层审批平均时间 / 总审批层级数

##### 成本控制类指标
- **费用偏差率** = |实际费用 - 预算费用| / 预算费用 × 100%
- **预算执行率** = 已使用预算 / 总预算 × 100%
- **单位处理成本** = 总运营成本 / 处理申请总数
- **费用集中度** = 前10%大额申请金额 / 总申请金额 × 100%
- **部门费用方差** = √(Σ(部门月度费用-部门平均费用)² / 月份数)

##### 合规风险类指标
- **合规检查通过率** = 通过合规检查的申请数 / 总申请数 × 100%
- **异常申请比例** = 标记为异常的申请数 / 总申请数 × 100%
- **政策违规率** = 违反公司政策的申请数 / 总申请数 × 100%
- **审计发现率** = 审计发现问题的申请数 / 抽查申请数 × 100%

### 费用管理专业业务规则

#### 关键词权重系数表
基于ServiceNow智能分类算法，不同关键词对费用金额的影响系数：
- **Travel权重系数** = 1.5 （基准金额 × 1.5）
- **Cloud权重系数** = 1.3 （基准金额 × 1.3）
- **Service权重系数** = 1.2 （基准金额 × 1.2）
- **Equipment权重系数** = 0.9 （基准金额 × 0.9）
- **Asset权重系数** = 0.8 （基准金额 × 0.8）

#### 审批矩阵决策规则
```
IF 金额 < $100 AND 关键词 ∈ {Equipment, Asset}
THEN 自动审批 (处理时间 = 0天)

IF 金额 ∈ [$100, $500] AND 部门 = Development
THEN 技术主管审批 (SLA = 2天)

IF 关键词 = Travel AND 金额 > $1000
THEN 多级审批 (财务 + 直属主管 + HR)

IF 关键词 = Cloud AND 地区 ∈ {Europe, Asia}
THEN 增加合规检查步骤 (额外1-2天)
```

#### 配置项(CI)分类体系
- **heart**: 核心业务系统相关费用，优先级高
- **truth**: 数据分析平台费用，需要数据团队评估
- **certain**: 确定性投资费用，按标准流程处理
- **among**: 跨部门协作费用，需要多方确认
- **material**: 物理资源费用，需要资产管理部门审核

### 部门业务特色与处理效率分析

#### Development部门（处理效率最高）
- **业务特点**：主要申请开发工具、云服务资源，技术导向明确
- **处理优势**：
  - 小额费用（<$100）占比高，享受快速审批通道（0天处理）
  - 技术需求明确，减少沟通成本
  - 预算规划相对稳定，审批标准化程度高
- **金额特征**：$100-$500区间费用处理时间较长（2天），需要技术评估

#### Finance部门（处理周期最长）
- **业务特点**：主要申请财务软件、审计服务，合规要求最严格
- **处理挑战**：
  - 多层级审批流程，涉及合规检查
  - 供应商资质审核严格
  - 与预算执行进度关联度高
- **改进空间**：可借鉴Development部门的小额快速审批机制

#### Sales部门
- **业务特点**：主要申请差旅费、客户接待费，时效性要求高
- **处理特征**：多为一次性申请，"Travel"关键词费用占比高
- **挑战**：差旅费用审批复杂，需要行程验证和标准核对

#### HR部门
- **业务特点**：主要申请培训费、员工福利，多为周期性申请
- **处理特征**：金额相对固定，但涉及人员管理政策审核
- **优化方向**：可建立周期性费用的自动审批机制

#### IT部门
- **业务特点**：主要申请硬件设备、软件许可，技术评估要求高
- **处理特征**：
  - "Equipment"和"Service"关键词费用较多
  - 需要技术架构团队评估
  - 与信息安全政策关联度高

#### Customer Support部门
- **业务特点**：主要申请客服工具、培训资源，与业务量关联度高
- **处理特征**：费用波动与客户服务负载相关，需要业务量数据支撑

#### Product Management部门
- **业务特点**：主要申请市场调研、产品工具，创新性项目较多
- **处理挑战**：
  - "Travel"费用处理时间长，尤其是市场调研相关差旅
  - 创新项目费用难以标准化，需要个案评估

### 地区业务特点

- **North America**：总部所在地，预算充足，审批流程标准化
- **Asia**：快速发展地区，申请量大，本地化需求多
- **Europe**：合规要求严格，审批周期较长，金额控制严格
- **Africa**：新兴市场，试点项目多，风险控制严格
- **South America**：成本敏感地区，小额申请居多，效率优先

### ServiceNow工作流专业概念

#### 状态转换矩阵
```
Submitted → Pending: 初始提交，等待分配审批人
Pending → Processed: 审批通过，费用获得批准
Pending → Declined: 审批拒绝，需要修改重新提交
Processed → Closed: 费用已支付，流程完结
Declined → Submitted: 修改后重新提交
```

#### SLA计算公式
- **响应时间SLA** = (首次响应时间 - 提交时间) ≤ 预定义响应时间
- **解决时间SLA** = (最终处理时间 - 提交时间) ≤ 预定义解决时间
- **SLA违约成本** = 违约次数 × 单次违约罚金

#### 工作流路由算法
```
路由权重 = 部门权重 × 金额权重 × 关键词权重 × 地区权重

部门权重表:
- Development: 0.8 (快速处理)
- Finance: 1.5 (严格审核)
- Sales: 1.0 (标准处理)
- IT: 1.2 (技术评估)

金额权重表:
- <$100: 0.5
- $100-$1000: 1.0
- $1000-$10000: 1.5
- >$10000: 2.0
```

#### 异常检测规则
- **金额异常**: |当前申请金额 - 历史平均金额| > 2σ
- **频率异常**: 单用户月申请次数 > 历史95分位数
- **时间异常**: 申请时间在非工作时间 (18:00-08:00)
- **地区异常**: 申请地区与用户常驻地区不符

### 费用类别专业分析模型

#### Assets类别风险评估模型
```
资产风险评分 = 折旧风险 + 技术风险 + 合规风险
折旧风险 = (资产原值 - 预计残值) / 使用年限 / 资产原值
技术风险 = 1 - (技术生命周期剩余年限 / 技术总生命周期)
合规风险 = 不合规项目数 / 总检查项目数
```

#### Services类别供应商评估矩阵
```
供应商综合评分 = 质量权重×质量得分 + 成本权重×成本得分 + 交付权重×交付得分
标准权重配置: 质量(40%) + 成本(35%) + 交付(25%) = 100%

质量得分 = (服务满意度评分 + 技术能力评分 + 历史表现评分) / 3
成本得分 = 100 - (实际成本 - 市场基准成本) / 市场基准成本 × 100
交付得分 = 按时交付率 × 100%
```

#### Travel类别合规检查清单
```
差旅合规性 = Σ(检查项权重 × 检查项得分)

必检项目:
- 行程合理性 (权重: 30%): 业务目的与行程匹配度
- 费用标准性 (权重: 25%): 是否符合公司差旅标准
- 审批完整性 (权重: 20%): 必要审批是否齐全
- 单据真实性 (权重: 15%): 发票、收据等单据验证
- 时间合理性 (权重: 10%): 差旅时间与业务需求匹配
```

#### Miscellaneous类别智能分类算法
```
分类置信度 = 关键词匹配度 × 历史相似度 × 金额合理度

IF 分类置信度 > 0.8 THEN 自动分类
ELIF 分类置信度 > 0.6 THEN 人工确认
ELSE 标记为"需要重新分类"

关键词匹配度 = 匹配关键词数 / 总关键词数
历史相似度 = 相似历史记录数 / 总历史记录数
金额合理度 = 1 - |当前金额 - 类别平均金额| / 类别金额标准差
```

### ServiceNow费用管理最佳实践

#### 工作流自动化策略
- **智能路由**：基于金额、部门、关键词自动分配审批人
- **并行审批**：对于复杂费用，技术评估和财务审批可并行进行
- **异常处理**：超时未处理的申请自动升级到上级审批人
- **批量处理**：相似类型的小额费用可批量审批

#### 预批准机制设计
- **年度预算框架**：各部门年初设定不同类别的预批准额度
- **常规费用模板**：标准化常见费用的申请模板和审批流程
- **紧急费用通道**：业务紧急情况下的快速审批机制
- **供应商白名单**：预审核供应商的费用可简化审批流程

#### 合规控制要点
- **多地区法规适配**：不同地区的税务、合规要求自动检查
- **预算控制**：实时预算余额检查，超预算自动预警
- **审计追踪**：完整的审批路径和决策依据记录
- **风险评估**：基于历史数据的费用风险评分机制

#### 性能优化技巧
- **缓存策略**：常用审批规则和用户权限信息缓存
- **数据分区**：按时间和部门对历史数据进行分区存储
- **负载均衡**：高峰期审批任务的智能分配
- **移动端优化**：支持移动设备的快速审批功能

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是该科技企业基于ServiceNow平台的费用管理系统分析师。根据最新的运营数据发现，虽然Development部门在处理效率方面表现最佳（平均处理周期最短），但其费用申请的最终通过率却明显低于其他部门，这一矛盾现象引起了管理层的关注。

**问题描述**：
请基于flag-40.csv数据，深入分析Development部门"处理快但通过率低"这一矛盾现象的根本原因，并提出针对性的改进建议。

**分析要求**：

1. 验证Development部门处理效率高但通过率低的现象
2. 从费用金额区间、关键词分类、申请类别等维度分析原因
3. 对比其他部门的成功经验，识别可借鉴的最佳实践
4. 结合ServiceNow平台特性，提出系统化改进方案

**领域知识要求**：

- 掌握**SLA达成率**、**首次通过率(FPY)**、**重新提交率**等专业指标计算
- 理解**关键词权重系数表**：Travel(1.5)、Cloud(1.3)、Service(1.2)、Equipment(0.9)、Asset(0.8)
- 熟悉**审批矩阵决策规则**和**工作流路由算法**中的部门权重配置
- 了解**配置项(CI)分类体系**：heart、truth、certain、among、material的业务含义
- 掌握**异常检测规则**：金额异常、频率异常、时间异常、地区异常的判定标准

**评估标准**：

- 能否正确计算和应用专业指标公式（如SLA达成率、首次通过率等）
- 是否运用了关键词权重系数和审批矩阵进行深度分析
- 能否识别并解释配置项分类对处理流程的影响
- 改进建议是否基于工作流路由算法和异常检测规则

### 题目二：带模板的归因问题

**题目背景**：
作为企业ServiceNow平台的运营分析师，你发现2024年第四季度（10-12月）费用申请处理效率出现显著下降，特别是"Travel"关键词相关的费用处理时间大幅增加，这与年底业务冲刺期的预期相矛盾。

**分析模板**：

#### 1. 问题确认与量化
- 计算第四季度与前三季度的平均处理周期对比
- 识别处理效率下降的具体表现
- **关键指标**：重点关注处理效率指数的变化

#### 2. 关键词影响分析
按ServiceNow智能分类体系分析：
- **高金额关键词**：Travel（+50%）、Cloud（+30%）、Service（+20%）的处理时间变化
- **标准金额关键词**：Equipment（-10%）、Asset（-20%）的处理时间变化
- **未分类费用**：Other类别的处理时间异常情况

#### 3. 金额区间处理策略分析
按ServiceNow审批策略分析各区间效率变化：
- **快速通道（<$100）**：是否仍保持0天处理
- **标准流程（$100-$500）**：是否超出1-2天标准
- **中级流程（$500-$5000）**：是否超出3-5天标准
- **高级流程（>$5000）**：复杂审批流程的瓶颈分析

#### 4. 部门效率对比分析
- **Development部门**：快速审批优势是否受到影响
- **Finance部门**：长周期处理是否进一步恶化
- **其他部门**：Travel费用处理的共性问题

#### 5. 交叉维度根因分析
- 部门+关键词组合的处理周期分析
- 金额区间+申请类别的处理瓶颈识别
- 地区+类型组合的合规审查影响

#### 6. ServiceNow平台优化建议
- **工作流优化**：针对Travel费用的自动化审批规则调整
- **预批准机制**：建立年底高频费用的预批准流程
- **智能路由**：优化不同金额区间的审批路径
- **系统集成**：加强与差旅管理系统的数据同步

**领域知识要求**：

- 掌握**平均解决时间(MTTR)**、**SLA违约成本**等关键指标计算公式
- 理解**工作流路由算法**中的权重计算：路由权重 = 部门权重 × 金额权重 × 关键词权重 × 地区权重
- 熟悉**状态转换矩阵**：Submitted→Pending→Processed/Declined的流程控制逻辑
- 掌握**费用类别专业分析模型**：资产风险评分、供应商评估矩阵、差旅合规检查清单
- 了解**异常检测规则**的数学模型：金额异常(2σ原则)、频率异常(95分位数)等

### 题目三：带模板的开放问题（周报）

**任务目标**：
你是企业费用管理部门的数据分析师，需要面向财务总监输出周度费用申请分析报告，帮助管理层了解过去7天（2024年10月21日-10月27日）的费用申请变化情况，以反映业务进展或提示管理风险。

#### 分析内容要求

##### 1. 当周概况

###### 1.1 当周数据概况

- 按照[部门]、[类别]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各个[部门]、[类别]每天的申请金额趋势图，并解读数据
- 按照[地区]、[申请类型]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各[地区]、[申请类型]每天的申请金额趋势图，并解读数据

###### 1.2 当周数据波动归因

- 使用表格，部门维度呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，说明当周环比变化在部门维度上的主要原因
- 围绕每个环比上周增长率的绝对值≥15%的部门维度：
  - 下钻到用户分析增长值，定位主要影响用户
  - 下钻到用户+类别维度分析增长值，定位主要影响的用户+类别维度
  - 下钻到用户+地区维度分析增长值，定位主要影响的用户+地区维度
- 基于上述的多轮下钻，明确主要影响变化的用户+类别+地区，形成表格，并解读数据

##### 2. 处理效率分析

- 当周各状态申请数量分布及变化
- 平均处理周期变化分析
- 异常处理案例识别

##### 3. 风险预警

- 大额申请预警（单笔金额>80000元）
- 长周期未处理申请预警（处理周期>30天）
- 部门预算使用率预警

#### 字段使用说明

##### 核心指标

- `日均申请金额`
- `单日申请金额`
- `申请通过率`
- `平均处理周期`

##### 核心维度

- **部门维度**：[部门]
- **用户维度**：[用户]
- **类别维度**：[类别]
- **地区维度**：[地点]
- **类型维度**：[类型]
- **状态维度**：[状态]

##### 时间维度

使用**[创建时间]**作为时间维度：

- **当周**：2024年10月21日-10月27日
- **上周**：2024年10月14日-10月20日
- **最近14天**：2024年10月14日-10月27日

**领域知识要求**：

- 掌握**费用偏差率**、**预算执行率**、**单位处理成本**、**费用集中度**等专业指标计算
- 理解**供应商综合评分模型**：质量权重(40%) + 成本权重(35%) + 交付权重(25%)
- 熟悉**差旅合规性检查清单**的权重配置和评分标准
- 掌握**智能分类算法**：分类置信度 = 关键词匹配度 × 历史相似度 × 金额合理度
- 了解**配置项(CI)分类体系**对不同业务流程的影响机制
- 理解**异常检测规则**在风险预警中的应用：2σ原则、95分位数阈值等

## 数据集扩展建议

### 扩展原则

只增加行数据，不修改现有列结构，确保题目的可执行性。

### 具体扩展方案

#### 1. 时间维度扩展

- **历史数据**：增加2023年全年数据，支持年度对比分析
- **未来数据**：增加2025年1-3月数据，支持趋势预测分析
- **高频时段**：增加年底预算冲刺期（11-12月）的密集数据

#### 2. 业务场景扩展

- **特殊事件**：增加公司重大项目期间的申请数据
- **季节性业务**：增加不同季度的典型业务申请数据
- **异常情况**：增加系统故障、政策变更等异常期间的数据

#### 3. 用户和组织扩展

- **用户层级**：增加管理层、普通员工等不同层级的申请数据
- **部门细分**：增加部门内小组的申请数据
- **跨部门协作**：增加跨部门项目的申请数据

#### 4. 地区和业务扩展

- **新兴市场**：增加新开拓地区的申请数据
- **业务类型**：增加新业务线的申请数据
- **合规要求**：增加不同地区合规要求下的申请数据

### 扩展效果预期

通过数据扩展，可以支持：

- 更复杂的时间序列分析
- 更深入的多维度交叉分析
- 更真实的业务场景模拟
- 更全面的异常检测和根因分析

## 实施建议

### 题目难度梯度

1. **题目一**：探索性分析，考查基础数据分析能力
2. **题目二**：结构化分析，考查逻辑思维和系统性分析能力
3. **题目三**：综合应用，考查业务理解和报告撰写能力

### 评估维度

- **数据处理能力**：数据清洗、计算、可视化
- **分析逻辑**：问题分解、因果推理、结论验证
- **领域知识应用**：业务理解、指标解释、建议可行性
- **表达能力**：结构清晰、重点突出、专业表述

### 预期成果

通过这三道题目的测试，可以全面评估大模型在企业数据分析场景下的：

- 数据探索和模式识别能力
- 结构化分析和逻辑推理能力
- 领域知识整合和应用能力
- 业务洞察和决策支持能力

## 总结

本项目基于flag-40.csv数据集成功构造了三道不同类型的分析题目，涵盖了从基础数据分析到高级业务洞察的完整能力谱系。通过企业费用申请管理这一贴近实际的业务场景，有效测试了大模型在复杂业务环境下的分析能力和领域知识应用水平。

项目的创新点在于：

1. 将抽象数据转化为具体业务场景
2. 设计了渐进式的难度梯度
3. 融合了丰富的领域知识要求
4. 提供了完整的数据扩展方案

这套题目体系不仅能够有效评估大模型的分析能力，还为类似的数据集问题构造提供了可复制的方法论。
