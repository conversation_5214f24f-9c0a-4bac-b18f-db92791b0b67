# Flag-40数据集问题构造项目文档

## 项目概述

### 项目目标

基于**flag-40.csv**数据集构造三道高质量的分析题目，包含不带模板的归因问题、带模板的归因问题和带模板的开放问题，旨在测试大模型在企业数据分析场景下的推理能力和领域知识应用能力。

### 核心要求

- 构造三种不同类型的题目，难度递进
- 部分题目需要领域知识才能正确解答
- 提供数据集扩展建议以支持更复杂的分析场景

## 数据集分析

### 业务场景定义

**flag-40.csv**数据集来源于某大型科技企业的**ServiceNow费用管理系统**（fm_expense_line表），记录了2024年全年各部门员工提交的费用申请及其处理情况。该系统是基于ServiceNow平台构建的企业级IT服务管理解决方案，专门用于财务费用的全生命周期管理。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义                         | 取值范围                                                            |
| -------- | -------- | -------------------------------- | ------------------------------------------------------------------- |
| 编号     | UUID     | 申请单唯一标识                   | 系统生成                                                            |
| 创建时间 | DateTime | 申请提交时间                     | 2024年全年                                                          |
| 金额     | Decimal  | 申请金额（元）                   | 1,000-150,000                                                       |
| 状态     | Enum     | 申请处理状态                     | Processed/Declined/Pending/Submitted                                |
| 简短描述 | Text     | 申请内容描述                     | 包含关键词：Equipment/Cloud/Asset/Service/Travel                    |
| 配置项   | Enum     | 申请配置类型                     | heart/truth/certain/among等                                         |
| 用户     | String   | 申请人用户名                     | 员工账号                                                            |
| 部门     | Enum     | 申请人所属部门                   | HR/Finance/Development/IT/Customer Support/Sales/Product Management |
| 类别     | Enum     | 费用类别                         | Assets/Miscellaneous/Services/Travel                                |
| 处理日期 | DateTime | 申请处理完成时间                 | 2024年全年                                                          |
| 来源ID   | UUID     | 关联的业务来源                   | 系统生成                                                            |
| 类型     | Enum     | 申请类型                         | One-time/Recurring                                                  |
| 地点     | Enum     | 业务发生地区                     | North America/Africa/Asia/Europe/South America                      |
| 处理周期 | Integer  | 处理天数（可为负值表示提前处理） | -300到+300天                                                        |

## 领域知识体系

### ServiceNow平台专业知识
**ServiceNow**是全球领先的企业级数字化工作流平台，其费用管理模块（fm_expense_line）具有以下特色：
- **工作流自动化**：支持多级审批流程，可根据金额、部门、类别自动路由
- **集成能力**：与HR、财务、采购等系统深度集成
- **合规管控**：内置合规检查规则，支持多地区法规要求
- **实时监控**：提供实时的费用追踪和预算控制功能
- **智能分类**：基于描述关键词自动分类费用类型和风险等级

### 企业费用管理核心指标

1. **申请通过率** = 已通过申请数 / 总申请数 × 100%
2. **平均处理周期** = 总处理天数 / 已处理申请数
3. **部门费用占比** = 某部门总费用 / 全公司总费用 × 100%
4. **地区费用分布** = 某地区总费用 / 全公司总费用 × 100%
5. **申请金额合规率** = 符合预算标准的申请数 / 总申请数 × 100%
6. **处理效率指数** = 标准处理时间 / 实际处理时间 × 100%

### 费用分类关键词体系
基于ServiceNow系统的智能分类规则，费用描述中的关键词直接影响审批流程和金额预期：

#### 高金额类别关键词
- **"Travel"**：差旅费用，通常金额较高（平均增幅50%），需要详细行程审批
- **"Cloud"**：云服务费用，金额波动大（平均增幅30%），需要技术部门评估
- **"Service"**：外部服务费用，金额中等（平均增幅20%），需要供应商资质审核

#### 标准金额类别关键词
- **"Equipment"**：设备采购，金额相对固定（平均减幅10%），有标准化采购流程
- **"Asset"**：资产相关费用，金额较低（平均减幅20%），审批流程简化

#### 特殊处理类别
- **"Other"**：未分类费用，需要人工审核，处理时间不确定

### 部门业务特色与处理效率分析

#### Development部门（处理效率最高）
- **业务特点**：主要申请开发工具、云服务资源，技术导向明确
- **处理优势**：
  - 小额费用（<$100）占比高，享受快速审批通道（0天处理）
  - 技术需求明确，减少沟通成本
  - 预算规划相对稳定，审批标准化程度高
- **金额特征**：$100-$500区间费用处理时间较长（2天），需要技术评估

#### Finance部门（处理周期最长）
- **业务特点**：主要申请财务软件、审计服务，合规要求最严格
- **处理挑战**：
  - 多层级审批流程，涉及合规检查
  - 供应商资质审核严格
  - 与预算执行进度关联度高
- **改进空间**：可借鉴Development部门的小额快速审批机制

#### Sales部门
- **业务特点**：主要申请差旅费、客户接待费，时效性要求高
- **处理特征**：多为一次性申请，"Travel"关键词费用占比高
- **挑战**：差旅费用审批复杂，需要行程验证和标准核对

#### HR部门
- **业务特点**：主要申请培训费、员工福利，多为周期性申请
- **处理特征**：金额相对固定，但涉及人员管理政策审核
- **优化方向**：可建立周期性费用的自动审批机制

#### IT部门
- **业务特点**：主要申请硬件设备、软件许可，技术评估要求高
- **处理特征**：
  - "Equipment"和"Service"关键词费用较多
  - 需要技术架构团队评估
  - 与信息安全政策关联度高

#### Customer Support部门
- **业务特点**：主要申请客服工具、培训资源，与业务量关联度高
- **处理特征**：费用波动与客户服务负载相关，需要业务量数据支撑

#### Product Management部门
- **业务特点**：主要申请市场调研、产品工具，创新性项目较多
- **处理挑战**：
  - "Travel"费用处理时间长，尤其是市场调研相关差旅
  - 创新项目费用难以标准化，需要个案评估

### 地区业务特点

- **North America**：总部所在地，预算充足，审批流程标准化
- **Asia**：快速发展地区，申请量大，本地化需求多
- **Europe**：合规要求严格，审批周期较长，金额控制严格
- **Africa**：新兴市场，试点项目多，风险控制严格
- **South America**：成本敏感地区，小额申请居多，效率优先

### 金额区间处理策略（基于ServiceNow最佳实践）

#### 快速审批通道（<$100）
- **处理时间**：0天（即时审批）
- **适用部门**：Development部门应用最广泛，占其申请的显著比例
- **业务逻辑**：小额费用风险低，采用自动化审批减少管理成本
- **系统设置**：预设审批规则，满足条件自动通过

#### 标准审批流程（$100-$500）
- **处理时间**：1-2天
- **审批要求**：需要直接主管审批
- **常见类别**：办公用品、小型设备、培训费用
- **注意事项**：Development部门此区间处理时间较长，可能存在流程优化空间

#### 中级审批流程（$500-$5000）
- **处理时间**：3-5天
- **审批要求**：需要部门经理和财务审批
- **常见类别**：软件许可、差旅费用、外部服务
- **关键控制点**：预算核对、供应商验证

#### 高级审批流程（$5000-$50000）
- **处理时间**：5-10天
- **审批要求**：需要高级管理层审批
- **常见类别**：大型设备采购、重要项目费用
- **特殊要求**：投资回报分析、多方案比较

#### 特殊审批流程（>$50000）
- **处理时间**：10-30天
- **审批要求**：需要董事会或CEO审批
- **严格控制**：详细的商业论证、风险评估、合规审查

### 申请类别特征与处理模式

#### Assets（资产类）
- **金额特征**：通常较高，但关键词影响下平均减幅20%
- **处理特点**：固定资产采购，审批层级高，处理周期长
- **优化建议**：建立标准化资产目录，简化常规设备审批

#### Services（服务类）
- **金额特征**：中等金额，关键词影响下平均增幅20%
- **处理特点**：服务采购，灵活性高，供应商评估重要
- **关键控制**：服务质量标准、供应商资质、合同条款审核

#### Travel（差旅类）
- **金额特征**：通常最高，关键词影响下平均增幅50%
- **处理挑战**：时效性强但审批复杂，各部门处理时间普遍较长
- **改进方向**：
  - 建立差旅标准和预批准机制
  - 简化常规商务差旅审批流程
  - IT和Product Management部门需要特别关注

#### Miscellaneous（其他类）
- **金额特征**：变化较大，难以预测
- **处理特点**：类型多样，需要个案分析和特殊审批
- **管理建议**：建立分类指导原则，减少"其他"类别的使用

### ServiceNow费用管理最佳实践

#### 工作流自动化策略
- **智能路由**：基于金额、部门、关键词自动分配审批人
- **并行审批**：对于复杂费用，技术评估和财务审批可并行进行
- **异常处理**：超时未处理的申请自动升级到上级审批人
- **批量处理**：相似类型的小额费用可批量审批

#### 预批准机制设计
- **年度预算框架**：各部门年初设定不同类别的预批准额度
- **常规费用模板**：标准化常见费用的申请模板和审批流程
- **紧急费用通道**：业务紧急情况下的快速审批机制
- **供应商白名单**：预审核供应商的费用可简化审批流程

#### 合规控制要点
- **多地区法规适配**：不同地区的税务、合规要求自动检查
- **预算控制**：实时预算余额检查，超预算自动预警
- **审计追踪**：完整的审批路径和决策依据记录
- **风险评估**：基于历史数据的费用风险评分机制

#### 性能优化技巧
- **缓存策略**：常用审批规则和用户权限信息缓存
- **数据分区**：按时间和部门对历史数据进行分区存储
- **负载均衡**：高峰期审批任务的智能分配
- **移动端优化**：支持移动设备的快速审批功能

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是该科技企业基于ServiceNow平台的费用管理系统分析师。根据最新的运营数据发现，虽然Development部门在处理效率方面表现最佳（平均处理周期最短），但其费用申请的最终通过率却明显低于其他部门，这一矛盾现象引起了管理层的关注。

**问题描述**：
请基于flag-40.csv数据，深入分析Development部门"处理快但通过率低"这一矛盾现象的根本原因，并提出针对性的改进建议。

**分析要求**：

1. 验证Development部门处理效率高但通过率低的现象
2. 从费用金额区间、关键词分类、申请类别等维度分析原因
3. 对比其他部门的成功经验，识别可借鉴的最佳实践
4. 结合ServiceNow平台特性，提出系统化改进方案

**领域知识要求**：

- 理解ServiceNow费用管理系统的工作流机制
- 掌握不同金额区间的审批策略（特别是<$100快速通道和$100-$500标准流程）
- 了解关键词分类对费用金额和审批复杂度的影响
- 熟悉Development部门的技术导向业务特点

**评估标准**：

- 能否识别出处理效率与通过率的矛盾关系
- 是否运用了金额区间分析和关键词分类知识
- 改进建议是否结合了ServiceNow平台的技术特性
- 分析逻辑的严密性和业务洞察的深度

### 题目二：带模板的归因问题

**题目背景**：
作为企业ServiceNow平台的运营分析师，你发现2024年第四季度（10-12月）费用申请处理效率出现显著下降，特别是"Travel"关键词相关的费用处理时间大幅增加，这与年底业务冲刺期的预期相矛盾。

**分析模板**：

#### 1. 问题确认与量化
- 计算第四季度与前三季度的平均处理周期对比
- 识别处理效率下降的具体表现
- **关键指标**：重点关注处理效率指数的变化

#### 2. 关键词影响分析
按ServiceNow智能分类体系分析：
- **高金额关键词**：Travel（+50%）、Cloud（+30%）、Service（+20%）的处理时间变化
- **标准金额关键词**：Equipment（-10%）、Asset（-20%）的处理时间变化
- **未分类费用**：Other类别的处理时间异常情况

#### 3. 金额区间处理策略分析
按ServiceNow审批策略分析各区间效率变化：
- **快速通道（<$100）**：是否仍保持0天处理
- **标准流程（$100-$500）**：是否超出1-2天标准
- **中级流程（$500-$5000）**：是否超出3-5天标准
- **高级流程（>$5000）**：复杂审批流程的瓶颈分析

#### 4. 部门效率对比分析
- **Development部门**：快速审批优势是否受到影响
- **Finance部门**：长周期处理是否进一步恶化
- **其他部门**：Travel费用处理的共性问题

#### 5. 交叉维度根因分析
- 部门+关键词组合的处理周期分析
- 金额区间+申请类别的处理瓶颈识别
- 地区+类型组合的合规审查影响

#### 6. ServiceNow平台优化建议
- **工作流优化**：针对Travel费用的自动化审批规则调整
- **预批准机制**：建立年底高频费用的预批准流程
- **智能路由**：优化不同金额区间的审批路径
- **系统集成**：加强与差旅管理系统的数据同步

**领域知识要求**：

- 了解季度业务周期特点
- 掌握费用审批流程的关键环节
- 理解不同维度对处理效率的影响机制

### 题目三：带模板的开放问题（周报）

**任务目标**：
你是企业费用管理部门的数据分析师，需要面向财务总监输出周度费用申请分析报告，帮助管理层了解过去7天（2024年10月21日-10月27日）的费用申请变化情况，以反映业务进展或提示管理风险。

#### 分析内容要求

##### 1. 当周概况

###### 1.1 当周数据概况

- 按照[部门]、[类别]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各个[部门]、[类别]每天的申请金额趋势图，并解读数据
- 按照[地区]、[申请类型]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各[地区]、[申请类型]每天的申请金额趋势图，并解读数据

###### 1.2 当周数据波动归因

- 使用表格，部门维度呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，说明当周环比变化在部门维度上的主要原因
- 围绕每个环比上周增长率的绝对值≥15%的部门维度：
  - 下钻到用户分析增长值，定位主要影响用户
  - 下钻到用户+类别维度分析增长值，定位主要影响的用户+类别维度
  - 下钻到用户+地区维度分析增长值，定位主要影响的用户+地区维度
- 基于上述的多轮下钻，明确主要影响变化的用户+类别+地区，形成表格，并解读数据

##### 2. 处理效率分析

- 当周各状态申请数量分布及变化
- 平均处理周期变化分析
- 异常处理案例识别

##### 3. 风险预警

- 大额申请预警（单笔金额>80000元）
- 长周期未处理申请预警（处理周期>30天）
- 部门预算使用率预警

#### 字段使用说明

##### 核心指标

- `日均申请金额`
- `单日申请金额`
- `申请通过率`
- `平均处理周期`

##### 核心维度

- **部门维度**：[部门]
- **用户维度**：[用户]
- **类别维度**：[类别]
- **地区维度**：[地点]
- **类型维度**：[类型]
- **状态维度**：[状态]

##### 时间维度

使用**[创建时间]**作为时间维度：

- **当周**：2024年10月21日-10月27日
- **上周**：2024年10月14日-10月20日
- **最近14天**：2024年10月14日-10月27日

**领域知识要求**：

- 掌握企业费用管理的关键指标体系
- 了解各部门的预算管理特点
- 理解费用申请的季节性和周期性规律
- 熟悉风险预警的业务标准

## 数据集扩展建议

### 扩展原则

只增加行数据，不修改现有列结构，确保题目的可执行性。

### 具体扩展方案

#### 1. 时间维度扩展

- **历史数据**：增加2023年全年数据，支持年度对比分析
- **未来数据**：增加2025年1-3月数据，支持趋势预测分析
- **高频时段**：增加年底预算冲刺期（11-12月）的密集数据

#### 2. 业务场景扩展

- **特殊事件**：增加公司重大项目期间的申请数据
- **季节性业务**：增加不同季度的典型业务申请数据
- **异常情况**：增加系统故障、政策变更等异常期间的数据

#### 3. 用户和组织扩展

- **用户层级**：增加管理层、普通员工等不同层级的申请数据
- **部门细分**：增加部门内小组的申请数据
- **跨部门协作**：增加跨部门项目的申请数据

#### 4. 地区和业务扩展

- **新兴市场**：增加新开拓地区的申请数据
- **业务类型**：增加新业务线的申请数据
- **合规要求**：增加不同地区合规要求下的申请数据

### 扩展效果预期

通过数据扩展，可以支持：

- 更复杂的时间序列分析
- 更深入的多维度交叉分析
- 更真实的业务场景模拟
- 更全面的异常检测和根因分析

## 实施建议

### 题目难度梯度

1. **题目一**：探索性分析，考查基础数据分析能力
2. **题目二**：结构化分析，考查逻辑思维和系统性分析能力
3. **题目三**：综合应用，考查业务理解和报告撰写能力

### 评估维度

- **数据处理能力**：数据清洗、计算、可视化
- **分析逻辑**：问题分解、因果推理、结论验证
- **领域知识应用**：业务理解、指标解释、建议可行性
- **表达能力**：结构清晰、重点突出、专业表述

### 预期成果

通过这三道题目的测试，可以全面评估大模型在企业数据分析场景下的：

- 数据探索和模式识别能力
- 结构化分析和逻辑推理能力
- 领域知识整合和应用能力
- 业务洞察和决策支持能力

## 总结

本项目基于flag-40.csv数据集成功构造了三道不同类型的分析题目，涵盖了从基础数据分析到高级业务洞察的完整能力谱系。通过企业费用申请管理这一贴近实际的业务场景，有效测试了大模型在复杂业务环境下的分析能力和领域知识应用水平。

项目的创新点在于：

1. 将抽象数据转化为具体业务场景
2. 设计了渐进式的难度梯度
3. 融合了丰富的领域知识要求
4. 提供了完整的数据扩展方案

这套题目体系不仅能够有效评估大模型的分析能力，还为类似的数据集问题构造提供了可复制的方法论。
