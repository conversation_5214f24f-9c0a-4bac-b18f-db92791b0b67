# Flag-40数据集问题构造项目文档

## 项目概述

### 项目目标

基于**flag-40.csv**数据集构造三道高质量的分析题目，包含不带模板的归因问题、带模板的归因问题和带模板的开放问题，旨在测试大模型在企业数据分析场景下的推理能力和领域知识应用能力。

### 核心要求

- 构造三种不同类型的题目，难度递进
- 部分题目需要领域知识才能正确解答
- 提供数据集扩展建议以支持更复杂的分析场景

## 数据集分析

### 业务场景定义

**flag-40.csv**数据集被定义为某大型科技企业的**费用申请管理系统**数据，记录了2024年全年各部门员工提交的费用申请及其处理情况。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义                         | 取值范围                                                            |
| -------- | -------- | -------------------------------- | ------------------------------------------------------------------- |
| 编号     | UUID     | 申请单唯一标识                   | 系统生成                                                            |
| 创建时间 | DateTime | 申请提交时间                     | 2024年全年                                                          |
| 金额     | Decimal  | 申请金额（元）                   | 1,000-150,000                                                       |
| 状态     | Enum     | 申请处理状态                     | Processed/Declined/Pending/Submitted                                |
| 简短描述 | Text     | 申请内容描述                     | 包含关键词：Equipment/Cloud/Asset/Service/Travel                    |
| 配置项   | Enum     | 申请配置类型                     | heart/truth/certain/among等                                         |
| 用户     | String   | 申请人用户名                     | 员工账号                                                            |
| 部门     | Enum     | 申请人所属部门                   | HR/Finance/Development/IT/Customer Support/Sales/Product Management |
| 类别     | Enum     | 费用类别                         | Assets/Miscellaneous/Services/Travel                                |
| 处理日期 | DateTime | 申请处理完成时间                 | 2024年全年                                                          |
| 来源ID   | UUID     | 关联的业务来源                   | 系统生成                                                            |
| 类型     | Enum     | 申请类型                         | One-time/Recurring                                                  |
| 地点     | Enum     | 业务发生地区                     | North America/Africa/Asia/Europe/South America                      |
| 处理周期 | Integer  | 处理天数（可为负值表示提前处理） | -300到+300天                                                        |

## 领域知识体系

### 企业费用管理核心指标

1. **申请通过率** = 已通过申请数 / 总申请数 × 100%
2. **平均处理周期** = 总处理天数 / 已处理申请数
3. **部门费用占比** = 某部门总费用 / 全公司总费用 × 100%
4. **地区费用分布** = 某地区总费用 / 全公司总费用 × 100%
5. **申请金额合规率** = 符合预算标准的申请数 / 总申请数 × 100%

### 部门业务特色

- **Development部门**：主要申请开发工具、云服务资源，金额通常较高，审批周期较长
- **Sales部门**：主要申请差旅费、客户接待费，时效性要求高，多为一次性申请
- **HR部门**：主要申请培训费、员工福利，多为周期性申请，金额相对固定
- **Finance部门**：主要申请财务软件、审计服务，审批流程最严格
- **IT部门**：主要申请硬件设备、软件许可，技术评估要求高
- **Customer Support部门**：主要申请客服工具、培训资源，与业务量关联度高
- **Product Management部门**：主要申请市场调研、产品工具，创新性项目较多

### 地区业务特点

- **North America**：总部所在地，预算充足，审批流程标准化
- **Asia**：快速发展地区，申请量大，本地化需求多
- **Europe**：合规要求严格，审批周期较长，金额控制严格
- **Africa**：新兴市场，试点项目多，风险控制严格
- **South America**：成本敏感地区，小额申请居多，效率优先

### 申请类别特征

- **Assets**：固定资产采购，金额大，审批层级高，处理周期长
- **Services**：服务采购，灵活性高，供应商评估重要
- **Travel**：差旅费用，时效性强，标准化程度高
- **Miscellaneous**：其他费用，类型多样，需要特殊审批

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是该科技企业的财务分析师。最近发现Development部门的费用申请通过率明显低于其他部门，引起了管理层的关注。

**问题描述**：
请基于flag-40.csv数据，分析Development部门申请通过率偏低的可能原因，并提出改进建议。

**分析要求**：

1. 计算各部门的申请通过率，验证问题的存在
2. 从多个维度分析Development部门的申请特征
3. 识别影响通过率的关键因素
4. 提出具体的改进措施

**领域知识要求**：

- 理解企业费用管理流程
- 掌握Development部门的业务特点
- 了解不同申请类别的审批标准

**评估标准**：

- 数据分析的全面性和准确性
- 原因分析的逻辑性和深度
- 改进建议的可操作性

### 题目二：带模板的归因问题

**题目背景**：
作为企业运营分析师，你需要分析2024年第四季度（10-12月）费用申请处理效率下降的原因。

**分析模板**：

#### 1. 问题确认

- 计算第四季度与前三季度的平均处理周期对比
- 识别处理效率下降的具体表现

#### 2. 维度下钻分析

按以下维度逐层分析处理周期变化：

- **部门维度**：各部门处理周期变化情况
- **地区维度**：各地区处理周期变化情况
- **类别维度**：各申请类别处理周期变化情况
- **金额维度**：不同金额区间的处理周期变化

#### 3. 交叉分析

- 部门+类别组合的处理周期分析
- 地区+金额组合的处理周期分析
- 识别影响最大的组合维度

#### 4. 根因定位

- 基于多维分析结果，定位处理效率下降的主要原因
- 量化各因素的影响程度

#### 5. 改进建议

- 针对识别出的根因提出具体改进措施
- 预估改进效果

**领域知识要求**：

- 了解季度业务周期特点
- 掌握费用审批流程的关键环节
- 理解不同维度对处理效率的影响机制

### 题目三：带模板的开放问题（周报）

**任务目标**：
你是企业费用管理部门的数据分析师，需要面向财务总监输出周度费用申请分析报告，帮助管理层了解过去7天（2024年10月21日-10月27日）的费用申请变化情况，以反映业务进展或提示管理风险。

#### 分析内容要求

##### 1. 当周概况

###### 1.1 当周数据概况

- 按照[部门]、[类别]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各个[部门]、[类别]每天的申请金额趋势图，并解读数据
- 按照[地区]、[申请类型]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各[地区]、[申请类型]每天的申请金额趋势图，并解读数据

###### 1.2 当周数据波动归因

- 使用表格，部门维度呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，说明当周环比变化在部门维度上的主要原因
- 围绕每个环比上周增长率的绝对值≥15%的部门维度：
  - 下钻到用户分析增长值，定位主要影响用户
  - 下钻到用户+类别维度分析增长值，定位主要影响的用户+类别维度
  - 下钻到用户+地区维度分析增长值，定位主要影响的用户+地区维度
- 基于上述的多轮下钻，明确主要影响变化的用户+类别+地区，形成表格，并解读数据

##### 2. 处理效率分析

- 当周各状态申请数量分布及变化
- 平均处理周期变化分析
- 异常处理案例识别

##### 3. 风险预警

- 大额申请预警（单笔金额>80000元）
- 长周期未处理申请预警（处理周期>30天）
- 部门预算使用率预警

#### 字段使用说明

##### 核心指标

- `日均申请金额`
- `单日申请金额`
- `申请通过率`
- `平均处理周期`

##### 核心维度

- **部门维度**：[部门]
- **用户维度**：[用户]
- **类别维度**：[类别]
- **地区维度**：[地点]
- **类型维度**：[类型]
- **状态维度**：[状态]

##### 时间维度

使用**[创建时间]**作为时间维度：

- **当周**：2024年10月21日-10月27日
- **上周**：2024年10月14日-10月20日
- **最近14天**：2024年10月14日-10月27日

**领域知识要求**：

- 掌握企业费用管理的关键指标体系
- 了解各部门的预算管理特点
- 理解费用申请的季节性和周期性规律
- 熟悉风险预警的业务标准

## 数据集扩展建议

### 扩展原则

只增加行数据，不修改现有列结构，确保题目的可执行性。

### 具体扩展方案

#### 1. 时间维度扩展

- **历史数据**：增加2023年全年数据，支持年度对比分析
- **未来数据**：增加2025年1-3月数据，支持趋势预测分析
- **高频时段**：增加年底预算冲刺期（11-12月）的密集数据

#### 2. 业务场景扩展

- **特殊事件**：增加公司重大项目期间的申请数据
- **季节性业务**：增加不同季度的典型业务申请数据
- **异常情况**：增加系统故障、政策变更等异常期间的数据

#### 3. 用户和组织扩展

- **用户层级**：增加管理层、普通员工等不同层级的申请数据
- **部门细分**：增加部门内小组的申请数据
- **跨部门协作**：增加跨部门项目的申请数据

#### 4. 地区和业务扩展

- **新兴市场**：增加新开拓地区的申请数据
- **业务类型**：增加新业务线的申请数据
- **合规要求**：增加不同地区合规要求下的申请数据

### 扩展效果预期

通过数据扩展，可以支持：

- 更复杂的时间序列分析
- 更深入的多维度交叉分析
- 更真实的业务场景模拟
- 更全面的异常检测和根因分析

## 实施建议

### 题目难度梯度

1. **题目一**：探索性分析，考查基础数据分析能力
2. **题目二**：结构化分析，考查逻辑思维和系统性分析能力
3. **题目三**：综合应用，考查业务理解和报告撰写能力

### 评估维度

- **数据处理能力**：数据清洗、计算、可视化
- **分析逻辑**：问题分解、因果推理、结论验证
- **领域知识应用**：业务理解、指标解释、建议可行性
- **表达能力**：结构清晰、重点突出、专业表述

### 预期成果

通过这三道题目的测试，可以全面评估大模型在企业数据分析场景下的：

- 数据探索和模式识别能力
- 结构化分析和逻辑推理能力
- 领域知识整合和应用能力
- 业务洞察和决策支持能力

## 总结

本项目基于flag-40.csv数据集成功构造了三道不同类型的分析题目，涵盖了从基础数据分析到高级业务洞察的完整能力谱系。通过企业费用申请管理这一贴近实际的业务场景，有效测试了大模型在复杂业务环境下的分析能力和领域知识应用水平。

项目的创新点在于：

1. 将抽象数据转化为具体业务场景
2. 设计了渐进式的难度梯度
3. 融合了丰富的领域知识要求
4. 提供了完整的数据扩展方案

这套题目体系不仅能够有效评估大模型的分析能力，还为类似的数据集问题构造提供了可复制的方法论。
