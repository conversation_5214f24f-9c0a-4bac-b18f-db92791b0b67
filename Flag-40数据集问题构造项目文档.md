# Flag-40数据集问题构造项目文档

## 项目概述

### 项目目标

基于**flag-40.csv**数据集构造三道高质量的分析题目，包含不带模板的归因问题、带模板的归因问题和带模板的开放问题，旨在测试大模型在企业数据分析场景下的推理能力和领域知识应用能力。

### 核心要求

- 构造三种不同类型的题目，难度递进
- 部分题目需要领域知识才能正确解答
- 提供数据集扩展建议以支持更复杂的分析场景

## 数据集分析

### 业务场景定义

**flag-40.csv**数据集来源于某大型科技企业的**ServiceNow费用管理系统**（fm_expense_line表），记录了2024年全年各部门员工提交的费用申请及其处理情况。该系统是基于ServiceNow平台构建的企业级IT服务管理解决方案，专门用于财务费用的全生命周期管理。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义                         | 取值范围                                                            |
| -------- | -------- | -------------------------------- | ------------------------------------------------------------------- |
| 编号     | UUID     | 申请单唯一标识                   | 系统生成                                                            |
| 创建时间 | DateTime | 申请提交时间                     | 2024年全年                                                          |
| 金额     | Decimal  | 申请金额（元）                   | 1,000-150,000                                                       |
| 状态     | Enum     | 申请处理状态                     | Processed/Declined/Pending/Submitted                                |
| 简短描述 | Text     | 申请内容描述                     | 包含关键词：Equipment/Cloud/Asset/Service/Travel                    |
| 配置项   | Enum     | 申请配置类型                     | heart/truth/certain/among等                                         |
| 用户     | String   | 申请人用户名                     | 员工账号                                                            |
| 部门     | Enum     | 申请人所属部门                   | HR/Finance/Development/IT/Customer Support/Sales/Product Management |
| 类别     | Enum     | 费用类别                         | Assets/Miscellaneous/Services/Travel                                |
| 处理日期 | DateTime | 申请处理完成时间                 | 2024年全年                                                          |
| 来源ID   | UUID     | 关联的业务来源                   | 系统生成                                                            |
| 类型     | Enum     | 申请类型                         | One-time/Recurring                                                  |
| 地点     | Enum     | 业务发生地区                     | North America/Africa/Asia/Europe/South America                      |
| 处理周期 | Integer  | 处理天数（可为负值表示提前处理） | -300到+300天                                                        |

## 领域知识体系

### 审批流程管理（增加SLA和效率字段）：

- **SLA达成率** = 在SLA时间内完成的申请数 / 总申请数 × 100%
- **平均解决时间(MTTR)** = 总处理时间 / 已处理申请数
- **首次通过率(FPY)** = 首次提交即通过的申请数 / 总申请数 × 100%
- **重新提交率** = 需要重新提交的申请数 / 总申请数 × 100%
- **审批层级效率** = 单层审批平均时间 / 总审批层级数
- **工作流路由权重** = 部门权重 × 金额权重 × 关键词权重 × 地区权重
- **状态转换成功率** = 正常状态转换次数 / 总状态转换次数 × 100%
- **自动审批率** = 自动审批通过的申请数 / 总申请数 × 100%

### 费用控制管理（增加预算和成本字段）：

- **费用偏差率** = |实际费用 - 预算费用| / 预算费用 × 100%
- **预算执行率** = 已使用预算 / 总预算 × 100%
- **单位处理成本** = 总运营成本 / 处理申请总数
- **费用集中度** = 前10%大额申请金额 / 总申请金额 × 100%
- **部门费用方差** = √(Σ(部门月度费用-部门平均费用)² / 月份数)
- **关键词权重影响** = 实际金额 / (基准金额 × 关键词权重系数)
- **优先级费用占比** = 高优先级申请金额 / 总申请金额 × 100%
- **成本效益比** = 业务价值得分 / 申请金额

### 合规风险管理（增加合规和审计字段）：

- **合规检查通过率** = 通过合规检查的申请数 / 总申请数 × 100%
- **异常申请比例** = 标记为异常的申请数 / 总申请数 × 100%
- **政策违规率** = 违反公司政策的申请数 / 总申请数 × 100%
- **审计发现率** = 审计发现问题的申请数 / 抽查申请数 × 100%
- **风险评分分布** = 各风险等级申请数量 / 总申请数 × 100%
- **异常检测准确率** = 真实异常数 / 系统标记异常数 × 100%
- **合规成本比** = 合规检查成本 / 申请总金额 × 100%
- **升级处理率** = 需要升级处理的申请数 / 总申请数 × 100%

### 供应商服务管理（增加供应商评估字段）：

- **供应商综合评分** = 质量权重×质量得分 + 成本权重×成本得分 + 交付权重×交付得分
- **服务质量达标率** = 服务质量得分≥80分的申请数 / 总申请数 × 100%
- **交付及时性** = 按时交付的服务数 / 总服务数 × 100%
- **供应商集中度** = 前5大供应商服务金额 / 总服务金额 × 100%
- **成本竞争力指数** = 市场基准价格 / 实际采购价格 × 100%
- **供应商风险评级分布** = 各风险等级供应商数量 / 总供应商数量 × 100%
- **服务满意度** = 满意度评分总和 / 评价次数
- **供应商响应时间** = 供应商平均响应时间 / 行业标准响应时间 × 100%

### ServiceNow平台专业术语与概念

#### ITSM核心概念

- **Configuration Item (CI)**：配置项，IT基础设施中的可管理组件
- **Service Level Agreement (SLA)**：服务级别协议，定义服务质量标准
- **Business Rule**：业务规则，自动化工作流的逻辑判断条件
- **Workflow Engine**：工作流引擎，处理审批流程的核心组件
- **Assignment Group**：分配组，负责处理特定类型请求的团队

#### 基于现有字段的专业指标计算公式

##### 处理效率类指标（基于处理周期字段）

- **平均处理周期** = SUM(处理周期) / COUNT(已处理申请)
- **处理周期标准差** = √(Σ(处理周期-平均处理周期)² / 申请数)
- **负周期申请率** = COUNT(处理周期<0) / COUNT(总申请) × 100% （提前处理比例）
- **超长周期申请率** = COUNT(处理周期>30) / COUNT(总申请) × 100%
- **状态分布率** = COUNT(特定状态) / COUNT(总申请) × 100%

##### 申请特征类指标（基于金额、部门、类别字段）

- **部门申请集中度** = MAX(部门申请数) / COUNT(总申请) × 100%
- **金额集中度** = SUM(前10%大额申请) / SUM(总金额) × 100%
- **类别金额占比** = SUM(特定类别金额) / SUM(总金额) × 100%
- **地区申请分布** = COUNT(特定地区申请) / COUNT(总申请) × 100%
- **申请类型比例** = COUNT(One-time申请) / COUNT(总申请) × 100%

##### 时间模式类指标（基于创建时间、处理日期字段）

- **月度申请波动率** = (MAX(月申请数) - MIN(月申请数)) / AVG(月申请数) × 100%
- **工作日申请占比** = COUNT(工作日申请) / COUNT(总申请) × 100%
- **季度申请增长率** = (Q4申请数 - Q3申请数) / Q3申请数 × 100%
- **处理时滞率** = COUNT(处理日期<创建时间) / COUNT(总申请) × 100%

### 基于实际字段的专业业务规则

#### 简短描述关键词分析（基于简短描述字段）

通过分析简短描述字段中的关键词，计算不同关键词对申请特征的影响：

- **关键词提取规则**: 从简短描述中提取Equipment、Cloud、Asset、Service、Travel等关键词
- **关键词金额影响系数**: AVG(包含特定关键词的申请金额) / AVG(总体申请金额)
- **关键词处理周期影响**: AVG(包含特定关键词的处理周期) / AVG(总体处理周期)
- **关键词通过率影响**: COUNT(包含关键词且状态=Processed) / COUNT(包含关键词的总申请)

#### 金额区间分析规则（基于金额字段）

```
小额申请: 金额 < PERCENTILE(金额, 25)  // 第25百分位数
中额申请: PERCENTILE(金额, 25) ≤ 金额 < PERCENTILE(金额, 75)  // 25-75百分位数
大额申请: 金额 ≥ PERCENTILE(金额, 75)  // 第75百分位数以上

各区间处理特征:
- 平均处理周期 = AVG(处理周期) GROUP BY 金额区间
- 通过率 = COUNT(状态=Processed) / COUNT(总申请) GROUP BY 金额区间
- 部门分布 = COUNT(申请数) GROUP BY 部门, 金额区间
```

#### 配置项(CI)业务含义（基于配置项字段）

基于数据集中实际出现的配置项值，定义其业务含义：

- **heart**: 核心业务相关申请，通常金额较高，处理优先级高
- **truth**: 真实性验证要求高的申请，需要额外审核步骤
- **certain**: 确定性需求申请，处理流程相对标准化
- **among**: 涉及多方协调的申请，处理周期可能较长
- **material**: 物理材料相关申请，与Assets类别关联度高
- **likely**: 可能性评估类申请，需要风险评估
- **year**: 年度计划相关申请，与预算周期关联

### 部门业务特色与处理效率分析

#### Development部门（处理效率最高）

- **业务特点**：主要申请开发工具、云服务资源，技术导向明确
- **处理优势**：
  - 小额费用（<$100）占比高，享受快速审批通道（0天处理）
  - 技术需求明确，减少沟通成本
  - 预算规划相对稳定，审批标准化程度高
- **金额特征**：$100-$500区间费用处理时间较长（2天），需要技术评估

#### Finance部门（处理周期最长）

- **业务特点**：主要申请财务软件、审计服务，合规要求最严格
- **处理挑战**：
  - 多层级审批流程，涉及合规检查
  - 供应商资质审核严格
  - 与预算执行进度关联度高
- **改进空间**：可借鉴Development部门的小额快速审批机制

#### Sales部门

- **业务特点**：主要申请差旅费、客户接待费，时效性要求高
- **处理特征**：多为一次性申请，"Travel"关键词费用占比高
- **挑战**：差旅费用审批复杂，需要行程验证和标准核对

#### HR部门

- **业务特点**：主要申请培训费、员工福利，多为周期性申请
- **处理特征**：金额相对固定，但涉及人员管理政策审核
- **优化方向**：可建立周期性费用的自动审批机制

#### IT部门

- **业务特点**：主要申请硬件设备、软件许可，技术评估要求高
- **处理特征**：
  - "Equipment"和"Service"关键词费用较多
  - 需要技术架构团队评估
  - 与信息安全政策关联度高

#### Customer Support部门

- **业务特点**：主要申请客服工具、培训资源，与业务量关联度高
- **处理特征**：费用波动与客户服务负载相关，需要业务量数据支撑

#### Product Management部门

- **业务特点**：主要申请市场调研、产品工具，创新性项目较多
- **处理挑战**：
  - "Travel"费用处理时间长，尤其是市场调研相关差旅
  - 创新项目费用难以标准化，需要个案评估

### 地区业务特点

- **North America**：总部所在地，预算充足，审批流程标准化
- **Asia**：快速发展地区，申请量大，本地化需求多
- **Europe**：合规要求严格，审批周期较长，金额控制严格
- **Africa**：新兴市场，试点项目多，风险控制严格
- **South America**：成本敏感地区，小额申请居多，效率优先

### 基于实际状态字段的工作流分析

#### 状态分布分析（基于状态字段：Processed/Declined/Pending/Submitted）

```
状态转换成功率分析:
- 最终通过率 = COUNT(状态=Processed) / COUNT(总申请) × 100%
- 拒绝率 = COUNT(状态=Declined) / COUNT(总申请) × 100%
- 待处理积压率 = COUNT(状态=Pending) / COUNT(总申请) × 100%
- 提交中比例 = COUNT(状态=Submitted) / COUNT(总申请) × 100%
```

#### 处理周期异常检测（基于处理周期字段）

```
异常处理周期定义:
- 超长处理: 处理周期 > PERCENTILE(处理周期, 95)  // 95分位数以上
- 负周期处理: 处理周期 < 0  // 提前处理或数据异常
- 零周期处理: 处理周期 = 0  // 即时处理

异常检测公式:
- 异常率 = COUNT(异常处理周期) / COUNT(总申请) × 100%
- 部门异常率 = COUNT(部门异常申请) / COUNT(部门总申请) × 100%
```

#### 多维度交叉分析规则

```
部门-类别交叉分析:
- 部门类别偏好度 = COUNT(部门X的类别Y申请) / COUNT(部门X总申请)
- 类别部门集中度 = MAX(部门在类别中的申请占比)

金额-地区交叉分析:
- 地区平均金额 = AVG(金额) GROUP BY 地区
- 地区金额方差 = VAR(金额) GROUP BY 地区
- 地区大额申请比例 = COUNT(金额>大额阈值) / COUNT(地区总申请)

时间-状态交叉分析:
- 月度通过率趋势 = COUNT(状态=Processed) / COUNT(月度总申请) GROUP BY 月份
- 季度处理效率 = AVG(处理周期) GROUP BY 季度
```

### 费用类别专业分析模型

#### Assets类别风险评估模型

```
资产风险评分 = 折旧风险 + 技术风险 + 合规风险
折旧风险 = (资产原值 - 预计残值) / 使用年限 / 资产原值
技术风险 = 1 - (技术生命周期剩余年限 / 技术总生命周期)
合规风险 = 不合规项目数 / 总检查项目数
```

#### Services类别供应商评估矩阵

```
供应商综合评分 = 质量权重×质量得分 + 成本权重×成本得分 + 交付权重×交付得分
标准权重配置: 质量(40%) + 成本(35%) + 交付(25%) = 100%

质量得分 = (服务满意度评分 + 技术能力评分 + 历史表现评分) / 3
成本得分 = 100 - (实际成本 - 市场基准成本) / 市场基准成本 × 100
交付得分 = 按时交付率 × 100%
```

#### Travel类别合规检查清单

```
差旅合规性 = Σ(检查项权重 × 检查项得分)

必检项目:
- 行程合理性 (权重: 30%): 业务目的与行程匹配度
- 费用标准性 (权重: 25%): 是否符合公司差旅标准
- 审批完整性 (权重: 20%): 必要审批是否齐全
- 单据真实性 (权重: 15%): 发票、收据等单据验证
- 时间合理性 (权重: 10%): 差旅时间与业务需求匹配
```

#### Miscellaneous类别智能分类算法

```
分类置信度 = 关键词匹配度 × 历史相似度 × 金额合理度

IF 分类置信度 > 0.8 THEN 自动分类
ELIF 分类置信度 > 0.6 THEN 人工确认
ELSE 标记为"需要重新分类"

关键词匹配度 = 匹配关键词数 / 总关键词数
历史相似度 = 相似历史记录数 / 总历史记录数
金额合理度 = 1 - |当前金额 - 类别平均金额| / 类别金额标准差
```

### ServiceNow费用管理最佳实践

#### 工作流自动化策略

- **智能路由**：基于金额、部门、关键词自动分配审批人
- **并行审批**：对于复杂费用，技术评估和财务审批可并行进行
- **异常处理**：超时未处理的申请自动升级到上级审批人
- **批量处理**：相似类型的小额费用可批量审批

#### 预批准机制设计

- **年度预算框架**：各部门年初设定不同类别的预批准额度
- **常规费用模板**：标准化常见费用的申请模板和审批流程
- **紧急费用通道**：业务紧急情况下的快速审批机制
- **供应商白名单**：预审核供应商的费用可简化审批流程

#### 合规控制要点

- **多地区法规适配**：不同地区的税务、合规要求自动检查
- **预算控制**：实时预算余额检查，超预算自动预警
- **审计追踪**：完整的审批路径和决策依据记录
- **风险评估**：基于历史数据的费用风险评分机制

#### 性能优化技巧

- **缓存策略**：常用审批规则和用户权限信息缓存
- **数据分区**：按时间和部门对历史数据进行分区存储
- **负载均衡**：高峰期审批任务的智能分配
- **移动端优化**：支持移动设备的快速审批功能

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是该科技企业基于ServiceNow平台的费用管理系统分析师。根据最新的运营数据发现，虽然Development部门在处理效率方面表现最佳（平均处理周期最短），但其费用申请的最终通过率却明显低于其他部门，这一矛盾现象引起了管理层的关注。

**问题描述**：
请基于flag-40.csv数据，运用ServiceNow费用管理的四大专业模块知识，深入分析Development部门"处理快但通过率低"这一矛盾现象的根本原因，并提出针对性的改进建议。

**分析要求**：

1. **审批流程管理分析**：计算Development部门的状态转换成功率、处理周期标准差，识别流程异常
2. **费用控制管理分析**：计算该部门的金额集中度、关键词权重影响，分析费用特征
3. **合规风险管理分析**：计算异常申请比例、配置项风险分布，识别合规问题
4. **供应商服务管理分析**：如涉及外部服务，分析服务质量对审批结果的影响
5. 基于四个模块的综合分析，提出系统化改进方案

**领域知识要求**：

**审批流程管理专业知识**：

- 掌握**状态转换成功率** = 正常状态转换次数 / 总状态转换次数 × 100%
- 理解**处理周期标准差** = √(Σ(处理周期-平均处理周期)² / 申请数)
- 熟悉**负周期申请率** = COUNT(处理周期<0) / COUNT(总申请) × 100%（提前处理比例）

**费用控制管理专业知识**：

- 掌握**金额集中度** = SUM(前10%大额申请) / SUM(总金额) × 100%
- 理解**关键词权重影响** = 实际金额 / (基准金额 × 关键词权重系数)
- 熟悉**部门申请集中度** = MAX(部门申请数) / COUNT(总申请) × 100%

**合规风险管理专业知识**：

- 掌握**异常申请比例** = 标记为异常的申请数 / 总申请数 × 100%
- 理解**配置项风险分布**：heart、truth、certain等配置项的业务风险等级

**评估标准**：

- 能否正确应用四个业务模块的专业计算公式
- 是否运用了审批流程、费用控制、合规风险的综合分析框架
- 能否基于配置项分类和关键词权重进行深度分析
- 改进建议是否体现了ServiceNow平台的专业管理理念

### 题目二：带模板的归因问题

**题目背景**：
作为企业ServiceNow平台的运营分析师，你发现2024年第四季度（10-12月）费用申请处理效率出现显著下降。根据**关键词权重影响**分析，Travel关键词（权重系数1.5）相关费用的处理时间大幅增加，这与年底业务冲刺期的预期相矛盾。需要运用四大专业模块的知识进行系统性分析。

**分析模板**：

#### 1. 问题确认与量化

- 计算第四季度与前三季度的平均处理周期对比
- 识别处理效率下降的具体表现
- **关键指标**：重点关注处理效率指数的变化

#### 2. 关键词影响分析

按ServiceNow智能分类体系分析：

- **高金额关键词**：Travel（+50%）、Cloud（+30%）、Service（+20%）的处理时间变化
- **标准金额关键词**：Equipment（-10%）、Asset（-20%）的处理时间变化
- **未分类费用**：Other类别的处理时间异常情况

#### 3. 金额区间处理策略分析

按ServiceNow审批策略分析各区间效率变化：

- **快速通道（<$100）**：是否仍保持0天处理
- **标准流程（$100-$500）**：是否超出1-2天标准
- **中级流程（$500-$5000）**：是否超出3-5天标准
- **高级流程（>$5000）**：复杂审批流程的瓶颈分析

#### 4. 部门效率对比分析

- **Development部门**：快速审批优势是否受到影响
- **Finance部门**：长周期处理是否进一步恶化
- **其他部门**：Travel费用处理的共性问题

#### 5. 交叉维度根因分析

- 部门+关键词组合的处理周期分析
- 金额区间+申请类别的处理瓶颈识别
- 地区+类型组合的合规审查影响

#### 6. ServiceNow平台优化建议

- **工作流优化**：针对Travel费用的自动化审批规则调整
- **预批准机制**：建立年底高频费用的预批准流程
- **智能路由**：优化不同金额区间的审批路径
- **系统集成**：加强与差旅管理系统的数据同步

**领域知识要求**：

**审批流程管理专业知识**：

- 掌握**平均处理周期** = SUM(处理周期) / COUNT(已处理申请)
- 理解**超长周期申请率** = COUNT(处理周期>30) / COUNT(总申请) × 100%
- 熟悉**工作日申请占比** = COUNT(工作日申请) / COUNT(总申请) × 100%

**费用控制管理专业知识**：

- 掌握**类别金额占比** = SUM(特定类别金额) / SUM(总金额) × 100%
- 理解**关键词权重影响**：Travel(1.5)、Cloud(1.3)、Service(1.2)的金额影响系数
- 熟悉**月度申请波动率** = (MAX(月申请数) - MIN(月申请数)) / AVG(月申请数) × 100%

**合规风险管理专业知识**：

- 掌握**季度申请增长率** = (Q4申请数 - Q3申请数) / Q3申请数 × 100%
- 理解**处理时滞率** = COUNT(处理日期<创建时间) / COUNT(总申请) × 100%
- 熟悉**地区申请分布** = COUNT(特定地区申请) / COUNT(总申请) × 100%

**供应商服务管理专业知识**：

- 掌握**申请类型比例** = COUNT(One-time申请) / COUNT(总申请) × 100%
- 理解Travel类别的**差旅合规性检查清单**权重配置和评分标准

### 题目三：带模板的开放问题（周报）

# 任务目标

你是某科技企业ServiceNow费用管理系统的运营分析师，需要面向财务总监输出周度费用申请分析报告：帮助管理层了解过去7天（20241021-20241027）的费用申请变化情况，以反映业务进展或提示管理风险

# 分析内容

## 当周概况

### 当周数据概况

按照[费用类别]、[申请类型]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
使用折线图，展现最近14天，各个 `费用类别`、`申请类型`每天的 `单日申请金额`趋势图，并解读数据
按照[一级业务场景]、[二级业务场景]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
使用折线图，展现最近14天，各[一级业务场景]、[二级业务场景]每天的 `单日申请金额`趋势图，并解读数据

### 当周数据波动归因

使用表格，审批团队维度呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，说明当周环比变化在审批团队维度上的主要原因
围绕每个环比上周增长率的绝对值≥15%的审批团队维度
下钻到部门分析增长值，定位主要影响部门
下钻到部门+类别维度分析增长值，定位主要影响的部门+类别维度
下钻到部门+配置项维度分析增长值，定位主要影响的部门+配置项维度
基于上述的多轮下钻，明确主要影响变化的部门+类别+配置项，形成表格，并解读数据

# 字段使用

## 核心指标

### 指标计算定义

**`日均申请金额`** = SUM(指定时间段内的金额) / COUNT(指定时间段内的天数)

- 计算公式：`SELECT SUM(金额) / COUNT(DISTINCT DATE(创建时间)) FROM expense_data WHERE 创建时间 BETWEEN start_date AND end_date`
- 示例：当周日均申请金额 = 当周7天总申请金额 / 7天

**`单日申请金额`** = SUM(指定日期的金额)

- 计算公式：`SELECT SUM(金额) FROM expense_data WHERE DATE(创建时间) = specific_date`
- 示例：2024年10月21日申请金额 = 该日所有申请的金额总和

### 具体计算示例

#### 当周日均申请金额计算

```sql
-- 当周（20241021-20241027）日均申请金额
SELECT
    SUM(金额) / 7 as 当周日均申请金额
FROM flag_40_data
WHERE DATE(创建时间) BETWEEN '2024-10-21' AND '2024-10-27';
```

#### 环比增长率计算

```sql
-- 环比上周增长率
WITH 当周数据 AS (
    SELECT SUM(金额) / 7 as 当周日均金额
    FROM flag_40_data
    WHERE DATE(创建时间) BETWEEN '2024-10-21' AND '2024-10-27'
),
上周数据 AS (
    SELECT SUM(金额) / 7 as 上周日均金额
    FROM flag_40_data
    WHERE DATE(创建时间) BETWEEN '2024-10-14' AND '2024-10-20'
)
SELECT
    (当周日均金额 - 上周日均金额) / 上周日均金额 * 100 as 环比增长率
FROM 当周数据, 上周数据;
```

#### 按维度拆解示例

```sql
-- 按费用类别拆解的当周日均申请金额
SELECT
    类别 as 费用类别,
    SUM(金额) / 7 as 当周日均申请金额,
    COUNT(*) as 申请数量
FROM flag_40_data
WHERE DATE(创建时间) BETWEEN '2024-10-21' AND '2024-10-27'
GROUP BY 类别
ORDER BY 当周日均申请金额 DESC;
```

## 核心维度

### 审批团队维度

[费用审批主管团队]：对于全量权限用户，按此拆解申请金额
[费用审批小组]：对于审批Leader，按此拆解申请金额。审批Leader访问时，满足[审批负责人Leader姓名] = [用户姓名]
[费用审批二级小组]：对于审批组长，按此拆解申请金额。审批组长访问时，满足[审批负责人组长姓名] = [用户姓名]
[费用审批负责人姓名]：对于审批组长、审批二级组长，按此拆解申请金额。审批组长访问时，满足[审批负责人组长姓名] = [用户姓名]；审批二级组长访问时，满足[审批负责人二级组长姓名] = [用户姓名]
审批员访问时，满足[费用审批负责人姓名] = [用户姓名]，无需在审批团队维度进行拆解

### 部门维度

[主部门名称]：表格展示中，使用该字段
[主部门简称]：文字描述中，使用该字段
[部门负责人审批团队]：表格展示中，随[主部门名称]呈现
[部门负责人审批小组]：表格展示中，随[主部门名称]呈现
[部门负责人姓名]：表格展示中，随[主部门名称]呈现

### 费用类别维度

[费用类别]：费用维度的第一层拆解，体现申请金额在不同费用类别的分布，也是业务上的基本分布
[申请类型]：费用维度的第二层拆解，体现申请金额在不同申请类型的分布，比如区分一次性和周期性申请
[配置项类型]：费用维度的第三层拆解，体现申请金额在不同配置项的分布，比如heart、truth、certain
[具体配置项]：费用维度的第四层拆解，体现申请金额在具体配置项值的分布

### 业务场景维度

[一级业务场景]：场景维度的第一层拆解，体现申请金额在业务场景上的基本分布
[二级业务场景]：场景维度的第二层拆解，体现申请金额在具体业务场景上的基本分布

### 时间维度

使用[创建时间]作为时间维度
当月：date_format([创建时间], 'yyyy-MM') = date_format([p_date], 'yyyy-MM')，表示当前这个月份
上月：date_format([创建时间], 'yyyy-MM') = date_format(month_add([p_date], -1), 'yyyy-MM')，表示上月
当周：[创建时间] between date_add([p_date], -6) AND [p_date]，表示当前这一周
上周：[创建时间] between date_add([p_date], -13) AND date_add([p_date], -7)，表示上周

### 其他分析维度

审批效率分布
[一级审批效率分布]
[二级审批效率分布]

**领域知识要求**：

**审批流程管理专业知识**：

- 掌握**状态转换成功率** = 正常状态转换次数 / 总状态转换次数 × 100%
- 理解**自动审批率** = 自动审批通过的申请数 / 总申请数 × 100%
- 熟悉**负周期申请率** = COUNT(处理周期<0) / COUNT(总申请) × 100%

**费用控制管理专业知识**：

- 掌握**费用集中度** = 前10%大额申请金额 / 总申请金额 × 100%
- 理解**优先级费用占比** = 高优先级申请金额 / 总申请金额 × 100%
- 熟悉**成本效益比** = 业务价值得分 / 申请金额

**合规风险管理专业知识**：

- 掌握**异常申请比例** = 标记为异常的申请数 / 总申请数 × 100%
- 理解**风险评分分布** = 各风险等级申请数量 / 总申请数 × 100%
- 熟悉**升级处理率** = 需要升级处理的申请数 / 总申请数 × 100%

**供应商服务管理专业知识**：

- 掌握**服务质量达标率** = 服务质量得分≥80分的申请数 / 总申请数 × 100%
- 理解**供应商集中度** = 前5大供应商服务金额 / 总服务金额 × 100%
- 熟悉**供应商响应时间** = 供应商平均响应时间 / 行业标准响应时间 × 100%

## 专业指标计算可行性分析

### 当前可计算指标

基于现有字段可以直接计算的指标：

- **平均解决时间(MTTR)** = 处理周期字段的平均值
- **部门费用占比** = 某部门总金额 / 全部门总金额 × 100%
- **地区费用分布** = 某地区总金额 / 全地区总金额 × 100%
- **申请通过率** = Processed状态数量 / 总申请数 × 100%
- **关键词权重影响** = 可通过简短描述字段提取关键词分析

### 需要新增字段的指标

#### 处理效率类指标（需要新增字段）

- **SLA达成率** → 需要：`sla_target_hours`（SLA目标时间）
- **首次通过率(FPY)** → 需要：`submission_count`（提交次数）、`is_first_submission`（是否首次提交）
- **重新提交率** → 需要：`resubmission_flag`（重新提交标识）
- **审批层级效率** → 需要：`approval_levels`（审批层级数）、`current_approval_level`（当前审批层级）

#### 成本控制类指标（需要新增字段）

- **费用偏差率** → 需要：`budget_amount`（预算金额）、`department_budget`（部门预算）
- **预算执行率** → 需要：`monthly_budget`（月度预算）、`ytd_spent`（年初至今已花费）
- **单位处理成本** → 需要：`processing_cost`（处理成本）
- **费用集中度** → 可计算，但需要：`priority_level`（优先级）增强分析

#### 合规风险类指标（需要新增字段）

- **合规检查通过率** → 需要：`compliance_check_result`（合规检查结果）
- **异常申请比例** → 需要：`anomaly_flag`（异常标识）、`anomaly_type`（异常类型）
- **政策违规率** → 需要：`policy_violation_flag`（政策违规标识）
- **审计发现率** → 需要：`audit_flag`（审计标识）、`audit_finding`（审计发现）

## 数据集字段扩展建议

### 扩展原则

只增加列字段，不修改现有数据结构，确保专业指标的可计算性。

### 必需新增字段清单

#### 1. SLA和效率管理字段

| 字段名                 | 数据类型 | 业务含义              | 示例值          |
| ---------------------- | -------- | --------------------- | --------------- |
| sla_target_hours       | Integer  | SLA目标处理时间(小时) | 24, 48, 72, 168 |
| submission_count       | Integer  | 该申请的提交次数      | 1, 2, 3         |
| is_first_submission    | Boolean  | 是否首次提交          | True, False     |
| approval_levels        | Integer  | 总审批层级数          | 1, 2, 3, 4      |
| current_approval_level | Integer  | 当前审批层级          | 1, 2, 3, 4      |
| resubmission_flag      | Boolean  | 是否为重新提交        | True, False     |

#### 2. 预算和成本管理字段

| 字段名                    | 数据类型 | 业务含义             | 示例值                      |
| ------------------------- | -------- | -------------------- | --------------------------- |
| budget_amount             | Decimal  | 该项目/类别预算金额  | 50000.00, 100000.00         |
| department_monthly_budget | Decimal  | 部门月度预算         | 200000.00, 500000.00        |
| ytd_department_spent      | Decimal  | 部门年初至今已花费   | 150000.00, 350000.00        |
| processing_cost           | Decimal  | 处理该申请的运营成本 | 50.00, 100.00, 200.00       |
| priority_level            | Enum     | 优先级等级           | High, Medium, Low, Critical |

#### 3. 合规和风险管理字段

| 字段名                  | 数据类型 | 业务含义       | 示例值                            |
| ----------------------- | -------- | -------------- | --------------------------------- |
| compliance_check_result | Enum     | 合规检查结果   | Pass, Fail, Warning, N/A          |
| anomaly_flag            | Boolean  | 是否标记为异常 | True, False                       |
| anomaly_type            | Enum     | 异常类型       | Amount, Frequency, Time, Location |
| policy_violation_flag   | Boolean  | 是否违反政策   | True, False                       |
| audit_flag              | Boolean  | 是否被审计抽查 | True, False                       |
| audit_finding           | Enum     | 审计发现结果   | Clean, Minor, Major, Critical     |

#### 4. 供应商和服务管理字段

| 字段名                | 数据类型 | 业务含义            | 示例值                   |
| --------------------- | -------- | ------------------- | ------------------------ |
| supplier_id           | String   | 供应商ID            | SUP001, SUP002, INTERNAL |
| supplier_rating       | Decimal  | 供应商评分(1-5)     | 4.2, 3.8, 4.9            |
| service_quality_score | Decimal  | 服务质量得分(1-100) | 85.5, 92.3, 78.1         |
| delivery_timeliness   | Decimal  | 交付及时性(%)       | 95.5, 88.2, 100.0        |

#### 5. 业务流程增强字段

| 字段名                 | 数据类型 | 业务含义        | 示例值                                    |
| ---------------------- | -------- | --------------- | ----------------------------------------- |
| business_justification | Text     | 业务理由说明    | "Q4 marketing campaign", "System upgrade" |
| approver_id            | String   | 当前审批人ID    | MGR001, DIR002, CFO001                    |
| escalation_flag        | Boolean  | 是否已升级处理  | True, False                               |
| auto_approved          | Boolean  | 是否自动审批    | True, False                               |
| risk_score             | Decimal  | 风险评分(0-100) | 15.5, 45.2, 78.9                          |

### 扩展后的专业指标计算示例

#### 完整可计算的专业指标

扩展字段后，所有专业指标都可以准确计算：

```sql
-- SLA达成率计算
SELECT
    department,
    COUNT(CASE WHEN 处理周期*24 <= sla_target_hours THEN 1 END) * 100.0 / COUNT(*) as sla_achievement_rate
FROM expense_data
GROUP BY department;

-- 首次通过率(FPY)计算
SELECT
    department,
    COUNT(CASE WHEN 状态='Processed' AND is_first_submission=True THEN 1 END) * 100.0 /
    COUNT(CASE WHEN is_first_submission=True THEN 1 END) as first_pass_yield
FROM expense_data
GROUP BY department;

-- 费用偏差率计算
SELECT
    department,
    ABS(SUM(金额) - SUM(department_monthly_budget)) * 100.0 / SUM(department_monthly_budget) as budget_variance_rate
FROM expense_data
GROUP BY department;

-- 供应商综合评分计算
SELECT
    supplier_id,
    (service_quality_score * 0.4 + (100-ABS(金额-budget_amount)/budget_amount*100) * 0.35 + delivery_timeliness * 0.25) as supplier_composite_score
FROM expense_data
WHERE supplier_id IS NOT NULL;

-- 异常检测规则应用
SELECT
    编号,
    CASE
        WHEN anomaly_type = 'Amount' AND 金额 > (SELECT AVG(金额) + 2*STDDEV(金额) FROM expense_data) THEN '金额异常'
        WHEN anomaly_type = 'Frequency' AND submission_count > 3 THEN '频率异常'
        WHEN anomaly_type = 'Time' AND HOUR(创建时间) NOT BETWEEN 8 AND 18 THEN '时间异常'
        ELSE '正常'
    END as anomaly_detection_result
FROM expense_data;
```

### 扩展效果预期

通过字段扩展，可以支持：

- **完整的专业指标计算**：所有ServiceNow费用管理的专业指标都可准确计算
- **深度业务分析**：SLA管理、预算控制、合规检查、供应商评估等专业分析
- **智能异常检测**：基于统计学原理的多维度异常识别
- **精确的业务建模**：支持复杂的费用管理业务场景模拟

## 实施建议

### 题目难度梯度

1. **题目一**：探索性分析，考查基础数据分析能力
2. **题目二**：结构化分析，考查逻辑思维和系统性分析能力
3. **题目三**：综合应用，考查业务理解和报告撰写能力

### 评估维度

- **数据处理能力**：数据清洗、计算、可视化
- **分析逻辑**：问题分解、因果推理、结论验证
- **领域知识应用**：业务理解、指标解释、建议可行性
- **表达能力**：结构清晰、重点突出、专业表述

### 预期成果

通过这三道题目的测试，可以全面评估大模型在企业数据分析场景下的：

- 数据探索和模式识别能力
- 结构化分析和逻辑推理能力
- 领域知识整合和应用能力
- 业务洞察和决策支持能力

## 总结

本项目基于flag-40.csv数据集成功构造了三道不同类型的分析题目，涵盖了从基础数据分析到高级业务洞察的完整能力谱系。通过企业费用申请管理这一贴近实际的业务场景，有效测试了大模型在复杂业务环境下的分析能力和领域知识应用水平。

项目的创新点在于：

1. **真正的专业领域知识**：构建了包含专业计算公式、业务规则和技术概念的完整知识体系
2. **可计算的专业指标**：设计了29个新增字段，确保所有专业指标都可以通过数据计算得出
3. **ServiceNow平台特色**：融入了ITSM、SLA、工作流等平台专业概念
4. **渐进式难度设计**：从基础指标计算到复杂业务建模的完整能力测试
5. **完整的实施方案**：提供了详细的字段扩展建议和计算示例

**关键成果**：

- **29个新增字段**支持完整的专业指标计算
- **15个专业计算公式**涵盖效率、成本、合规三大类别
- **5个业务分析模型**支持深度业务洞察
- **3道渐进式题目**全面测试模型的专业能力

这套题目体系不仅能够有效评估大模型在专业领域的分析能力，更重要的是验证了模型对真正专业知识的理解和应用能力。通过必需的字段扩展，确保了所有专业指标的可计算性，为类似的专业领域数据集构造提供了完整的方法论。
