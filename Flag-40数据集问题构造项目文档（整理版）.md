# Flag-40数据集问题构造项目文档

## 项目概述

基于**flag-40.csv**数据集构造三道分析题目：不带模板的归因问题、带模板的归因问题、带模板的开放问题。

## 数据集分析

**flag-40.csv**数据集：ServiceNow费用管理系统，记录2024年各部门费用申请及处理情况。

### 数据结构分析

| 字段名   | 数据类型 | 业务含义                         | 取值范围                                                            |
| -------- | -------- | -------------------------------- | ------------------------------------------------------------------- |
| 编号     | UUID     | 申请单唯一标识                   | 系统生成                                                            |
| 创建时间 | DateTime | 申请提交时间                     | 2024年全年                                                          |
| 金额     | Decimal  | 申请金额（元）                   | 1,000-150,000                                                       |
| 状态     | Enum     | 申请处理状态                     | Processed/Declined/Pending/Submitted                                |
| 简短描述 | Text     | 申请内容描述                     | 包含关键词：Equipment/Cloud/Asset/Service/Travel                    |
| 配置项   | Enum     | 申请配置类型                     | heart/truth/certain/among等                                         |
| 用户     | String   | 申请人用户名                     | 员工账号                                                            |
| 部门     | Enum     | 申请人所属部门                   | HR/Finance/Development/IT/Customer Support/Sales/Product Management |
| 类别     | Enum     | 费用类别                         | Assets/Miscellaneous/Services/Travel                                |
| 处理日期 | DateTime | 申请处理完成时间                 | 2024年全年                                                          |
| 来源ID   | UUID     | 关联的业务来源                   | 系统生成                                                            |
| 类型     | Enum     | 申请类型                         | One-time/Recurring                                                  |
| 地点     | Enum     | 业务发生地区                     | North America/Africa/Asia/Europe/South America                      |
| 处理周期 | Integer  | 处理天数（可为负值表示提前处理） | -300到+300天                                                        |

### 需要增加的专业字段

| 字段名       | 数据类型 | 业务含义                | 示例值                   |
| ------------ | -------- | ----------------------- | ------------------------ |
| SLA目标时间  | Integer  | SLA规定的处理时间(小时) | 24, 48, 72, 168          |
| 是否首次提交 | Boolean  | 是否为首次提交          | True, False              |
| 预算金额     | Decimal  | 该项目/类别预算金额     | 50000.00, 100000.00      |
| 审批层级数   | Integer  | 总审批层级数            | 1, 2, 3, 4               |
| 当前审批层级 | Integer  | 当前所在审批层级        | 1, 2, 3, 4               |
| 风险等级     | Enum     | 风险评估等级            | 低风险, 中风险, 高风险   |
| 异常标识     | Boolean  | 是否标记为异常          | True, False              |
| 合规检查结果 | Enum     | 合规检查结果            | 通过, 不通过, 待检查     |
| 供应商ID     | String   | 供应商标识              | SUP001, SUP002, INTERNAL |
| 服务质量评分 | Decimal  | 服务质量得分(1-100)     | 85.5, 92.3, 78.1         |

## 领域知识体系

### 核心指标计算公式

#### 审批效率指标（增加SLA管理字段）：

- **申请通过率** = Processed状态申请数 / 总申请数 × 100%
- **平均处理周期** = 处理周期总和 / 已处理申请数
- **SLA达成率** = 在SLA时间内完成的申请数 / 总申请数 × 100%
- **首次通过率** = 首次提交即通过的申请数 / 总申请数 × 100%

#### 费用控制指标（增加预算管理字段）：

- **费用集中度** = 前10%大额申请金额 / 总申请金额 × 100%
- **预算执行率** = 已使用预算 / 总预算 × 100%
- **费用偏差率** = |实际费用 - 预算费用| / 预算费用 × 100%
- **关键词金额影响** = 包含特定关键词的申请平均金额 / 总体平均金额

#### 审批流程指标（增加审批层级字段）：

- **审批层级效率** = 单层审批平均时间 / 总审批层级数
- **审批瓶颈率** = 超时审批层级数 / 总审批层级数 × 100%
- **自动审批率** = 自动审批通过的申请数 / 总申请数 × 100%
- **审批退回率** = 被退回修改的申请数 / 总申请数 × 100%

#### 风险管控指标（增加风险评估字段）：

- **异常申请比例** = 标记为异常的申请数 / 总申请数 × 100%
- **高风险申请率** = 高风险等级申请数 / 总申请数 × 100%
- **合规检查通过率** = 通过合规检查的申请数 / 总申请数 × 100%
- **风险预警准确率** = 实际发生风险数 / 系统预警数 × 100%

#### 供应商管理指标（增加供应商评估字段）：

- **供应商综合评分** = 质量权重×质量得分 + 成本权重×成本得分 + 交付权重×交付得分
- **服务质量达标率** = 服务质量得分≥80分的申请数 / 总申请数 × 100%
- **供应商集中度** = 前5大供应商服务金额 / 总服务金额 × 100%
- **交付及时性** = 按时交付的服务数 / 总服务数 × 100%

## 构造题目

### 题目一：不带模板的归因问题

**题目背景**：
你是该科技企业基于ServiceNow平台的费用管理系统分析师。根据最新的运营数据发现，虽然Development部门在处理效率方面表现最佳（平均处理周期最短），但其费用申请的最终通过率却明显低于其他部门，这一矛盾现象引起了管理层的关注。

**问题描述**：
请基于flag-40.csv数据，运用ServiceNow费用管理的四大专业模块知识，深入分析Development部门"处理快但通过率低"这一矛盾现象的根本原因，并提出针对性的改进建议。

**分析要求**：

1. **审批流程管理分析**：计算Development部门的状态转换成功率、处理周期标准差，识别流程异常
2. **费用控制管理分析**：计算该部门的金额集中度、关键词权重影响，分析费用特征
3. **合规风险管理分析**：计算异常申请比例、配置项风险分布，识别合规问题
4. **业务场景管理分析**：如涉及外部服务，分析服务质量对审批结果的影响
5. 基于四个模块的综合分析，提出系统化改进方案

**领域知识要求**：

- 掌握**申请通过率**、**SLA达成率**、**首次通过率**的计算方法
- 理解**费用集中度**、**预算执行率**、**费用偏差率**的分析方法
- 熟悉**审批层级效率**、**审批瓶颈率**、**自动审批率**的流程分析
- 了解**异常申请比例**、**高风险申请率**、**合规检查通过率**的风险评估
- 掌握**供应商综合评分**、**服务质量达标率**的供应商管理指标

**评估标准**：

- 能否正确应用四个业务模块的专业计算公式
- 是否运用了审批流程、费用控制、合规风险的综合分析框架
- 能否基于配置项分类和关键词权重进行深度分析
- 改进建议是否体现了ServiceNow平台的专业管理理念

### 题目二：带模板的归因问题

**题目背景**：
作为企业ServiceNow平台的运营分析师，你发现2024年第四季度（10-12月）费用申请处理效率出现显著下降。根据**关键词权重影响**分析，Travel关键词（权重系数1.5）相关费用的处理时间大幅增加，这与年底业务冲刺期的预期相矛盾。需要运用四大专业模块的知识进行系统性分析。

**分析要求**：
1. 计算第四季度与前三季度的平均处理周期对比
2. 按关键词分析处理时间变化：Travel、Cloud、Service、Equipment、Asset
3. 按金额区间分析效率变化：<$100、$100-$500、$500-$5000、>$5000
4. 按部门分析效率对比：Development、Finance等部门的处理时间变化
5. 提出针对性的优化建议

**领域知识要求**：

- 掌握**平均处理周期**、**超长周期申请率**的计算方法
- 理解**类别金额占比**、**关键词金额影响**的分析方法
- 熟悉**季度申请增长率**、**地区申请分布**的对比分析方法
- 了解Travel、Cloud、Service等关键词对费用金额和处理时间的影响规律

### 题目三：带模板的开放问题（周报）

# 任务目标

你是某科技企业ServiceNow费用管理系统的运营分析师，需要面向财务总监输出周度费用申请分析报告：帮助管理层了解过去7天（20241021-20241027）的费用申请变化情况，以反映业务进展或提示管理风险

# 分析内容

## 当周概况

### 当周数据概况

按照[费用类别]、[申请类型]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
使用折线图，展现最近14天，各个 `费用类别`、`申请类型`每天的 `单日申请金额`趋势图，并解读数据
按照[一级业务场景]、[二级业务场景]呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，使用表格展示，并解读数据
使用折线图，展现最近14天，各[一级业务场景]、[二级业务场景]每天的 `单日申请金额`趋势图，并解读数据

### 当周数据波动归因

使用表格，审批团队维度呈现当周日均申请金额、环比上周增长率、环比上周增长值，按日均金额降序排列，说明当周环比变化在审批团队维度上的主要原因
围绕每个环比上周增长率的绝对值≥15%的审批团队维度
下钻到部门分析增长值，定位主要影响部门
下钻到部门+类别维度分析增长值，定位主要影响的部门+类别维度
下钻到部门+配置项维度分析增长值，定位主要影响的部门+配置项维度
基于上述的多轮下钻，明确主要影响变化的部门+类别+配置项，形成表格，并解读数据

# 字段使用

# 字段使用
- **日均申请金额** = 指定时间段内金额总和 / 时间段天数
- **单日申请金额** = 指定日期的金额总和
- **环比增长率** = (当周指标 - 上周指标) / 上周指标 × 100%

**领域知识要求**：

- 掌握**日均申请金额**、**单日申请金额**的计算方法
- 理解**环比增长率**、**环比增长值**的计算公式
- 熟悉多维度下钻分析方法：部门→部门+类别→部门+配置项
- 了解ServiceNow费用管理系统的周报分析框架和业务逻辑


