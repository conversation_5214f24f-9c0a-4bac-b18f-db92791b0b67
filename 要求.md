# Flag-40数据集问题构造任务要求

## 任务概述

基于**flag-40.csv**数据集构造出三道题目，包含以下三个类型：

- 不带模板的归因问题
- 带模板的归因问题
- 带模板的开放问题

**核心要求**：

- 其中一部分题目需要领域知识才能解题
- 基于构造的问题，考虑数据集的扩展方案（只增加行，不改列）

## 题目类型分类

### 1. 归因问题

#### 1.1 不带模板

#### 1.2 带模板

### 2. 开放问题

#### 2.1 周报类问题

## 数据集扩展建议

| 数据集类型                        | 业务领域   | 领域知识要求                                                                                                                                                                                                                                                                                                                                           | 扩展字段建议 |
| --------------------------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------ |
| **帆软数据集(flag-40.csv)** | 零售、商业 | **用户维度指标**：``• 客单价 = 销售总金额/有交易的顾客总数``• 件单价 = 销售总金额/销售总数量``• 连带率 = 销售总数量/成交订单总数                                                                                                                                                                                         | 增加用户字段 |
|                                   |            | **商品维度指标**：``• 折扣率 = 商品实收金额/商品标准零售价金额``• 品类结构占比 = 某品类销售额/总销售额``• 价位段占比 = 某价位段销售额/总销售额``• 正价销售占比 = 正价商品销售额/总销售额``• 价格弹性指数：商品价格变化1%时，商品销量变化的百分比``• 前十大销售及占比``• 前十大库存及占比 | 增加折扣信息 |
|                                   |            | **促销维度指标**：``• 费销比 = 促销费用金额/促销期间的销售额``• 目标完成率 = 促销期间销售金额/促销目标销售金额``• 促销爆发度 = (促销期间平均权重销售额-促销前平均权重销售额)/促销前平均权重销售额``• 促销衰减度 = (促销期间平均权重销售额-促销后平均销售额)/促销前平均权重销售额                                 | 增加促销字段 |

## 领域知识说明

> **重要提示**：领域知识不仅仅包含计算指标，业务知识也同样重要。
>
> **示例**：数据平台的产品销量分析 - 需要自定义数据平台包含哪些产品（只要合理即可）
>
> **核心原则**：如果不告诉模型相关背景知识，模型就无法正确解答题目
>
> **建议**：优先选择具有该数据集/行业特色的专业知识

## 问题示例

### 示例：大模型业务周度分析报告

**任务目标**：
你是某科技公司大模型业务的经营分析师，需要面向销售同学输出周度分析报告，帮助销售了解过去7天（20250410-20250416）的大模型业务变化情况，以反映业务进展或提示业务风险。

#### 分析内容要求

##### 1. 当周概况

###### 1.1 当周数据概况

- 按照[大模型类型]、[模型简称]呈现当周日均Tokens、环比上周增长率、环比上周增长值，按日均Tokens降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各个 `大模型类型`、`模型简称`每天的 `单日原始调用量(Tokens)`趋势图，并解读数据
- 按照[一级应用场景]、[二级应用场景]呈现当周日均Tokens、环比上周增长率、环比上周增长值，按日均Tokens降序排列，使用表格展示，并解读数据
- 使用折线图，展现最近14天，各[一级应用场景]、[二级应用场景]每天的 `单日原始调用量(Tokens)`趋势图，并解读数据

###### 1.2 当周数据波动归因

- 使用表格，团队维度呈现当周日均Tokens、环比上周增长率、环比上周增长值，按日均Tokens降序排列，说明当周环比变化在团队维度上的主要原因
- 围绕每个环比上周增长率的绝对值≥10%的团队维度：
  - 下钻到客户分析增长值，定位主要影响客户
  - 下钻到客户+模型维度分析增长值，定位主要影响的客户+模型维度
  - 下钻到客户+场景维度分析增长值，定位主要影响的客户+场景维度
- 基于上述的多轮下钻，明确主要影响变化的客户+模型+场景，形成表格，并解读数据

#### 字段使用说明

##### 核心指标

- `日均原始调用量(Tokens)`
- `单日原始调用量(Tokens)`

##### 核心维度

###### 团队维度

- **[用量归属人销售团队]**：对于全量权限用户，按此拆解Tokens
- **[用量归属人销售小组]**：对于销售Leader，按此拆解Tokens。销售Leader访问时，满足[用量归属人Leader姓名] = [用户姓名]
- **[用量归属人二级小组]**：对于销售组长，按此拆解Tokens。销售组长访问时，满足[用量归属人组长姓名] = [用户姓名]
- **[用量归属人姓名]**：对于销售组长、销售二级组长，按此拆解Tokens
  - 销售组长访问时，满足[用量归属人组长姓名] = [用户姓名]
  - 销售二级组长访问时，满足[用量归属人二级组长姓名] = [用户姓名]
- **销售访问时**：满足[用量归属人姓名] = [用户姓名]，无需在团队维度进行拆解

###### 客户维度

- **[主客户名称]**：表格展示中使用该字段
- **[主客户简称]**：文字描述中使用该字段
- **[客户所有人销售团队]**：表格展示中，随[主客户名称]呈现
- **[客户所有人销售小组]**：表格展示中，随[主客户名称]呈现
- **[客户所有人姓名]**：表格展示中，随[主客户名称]呈现

###### 模型维度

- **[大模型类型]**：模型维度的第一层拆解，体现Tokens在不同大模型类型的分布，也是业务上的基本分布
- **[模型简称]**：模型维度的第二层拆解，体现Tokens在不同模型的分布，比如区分某模型和某第三方模型
- **[模型家族]**：模型维度的第三层拆解，体现某模型Tokens在不同模型分支的分布，比如某模型-Pro、某模型-VLM
- **[模型名称]**：模型维度的第四层拆解，体现某模型Tokens在不同模型的分布，比如ModelA-1-5-pro-32k、ModelA-1-5-lite-32k

###### 场景维度

- **[一级应用场景]**：场景维度的第一层拆解，体现Tokens在场景上的基本分布
- **[二级应用场景]**：场景维度的第二层拆解，体现Tokens在具体应用场景上的基本分布

###### 时间维度

使用**[调用日期]**作为时间维度：

- **当月**：`date_format([调用日期], 'yyyy-MM') = date_format([p_date], 'yyyy-MM')`，表示当前这个月份
- **上月**：`date_format([调用日期], 'yyyy-MM') = date_format(month_add([p_date], -1), 'yyyy-MM')`，表示上月
- **当周**：`[调用日期] between date_add([p_date], -6) AND [p_date]`，表示当前这一周
- **上周**：`[调用日期] between date_add([p_date], -13) AND date_add([p_date], -7)`，表示上周

###### 其他分析维度

- **场景流量分布**
  - [一级应用场景流量分布]
  - [二级应用场景流量分布]
